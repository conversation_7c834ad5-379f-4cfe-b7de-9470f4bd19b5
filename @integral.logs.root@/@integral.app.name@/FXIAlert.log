2023-10-13 12:03:45,849  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:03:45 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:04:20,264  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:04:20 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:04:29,517  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:04:29 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:04:35,495  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:04:35 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:04:41,539  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:04:41 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:04:50,397  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:04:50 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:05:05,041  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:05:05 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:05:24,424  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:05:24 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:05:52,074  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:05:51 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:05:58,950  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:05:58 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:06:26,177  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:06:26 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:06:49,375  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:06:49 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:06:56,300  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:06:56 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:07:01,897  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:07:01 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:07:08,745  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:07:08 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:07:17,301  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:07:17 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:07:22,943  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:07:22 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:07:45,200  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:07:45 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:07:55,466  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:07:55 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:08:00,856  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:08:00 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-10-13 12:08:07,739  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Fri Oct 13 12:08:07 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
