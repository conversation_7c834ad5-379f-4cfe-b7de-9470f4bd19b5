2023-05-10 14:42:21,856  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:42:21 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:42:32,023  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:42:32 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:42:40,267  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:42:40 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:42:46,611  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:42:46 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:42:53,062  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:42:53 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:43:02,499  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:43:02 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:43:08,688  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:43:08 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:43:15,114  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:43:15 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:43:21,669  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:43:21 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:43:28,995  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:43:28 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:43:55,888  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:43:55 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:44:01,437  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:44:01 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:44:08,709  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:44:08 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:44:14,599  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:44:14 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:44:21,886  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:44:21 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:44:31,408  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:44:31 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
2023-05-10 14:44:37,367  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Wed May 10 14:44:37 PDT 2023&peeyushs-MacBook-Pro.local&TESTVS&null
