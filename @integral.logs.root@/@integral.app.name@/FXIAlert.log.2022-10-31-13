2022-10-31 13:36:03,579  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:03 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:12,860  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:12 PDT 2022&peeyushs-<PERSON>Book-Pro.local&TESTVS&null
2022-10-31 13:36:20,477  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:20 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:26,527  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:26 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:32,638  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:32 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:41,283  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:41 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:47,127  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:47 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:53,378  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:53 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:36:59,421  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:36:59 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:37:32,754  INFO Test worker #[-] RDS-XX-002&RDS.REQUEST.TIMEOUT&com.integral.rds.client.net.http.HttpConnector&TimeoutException:Request timed out. exchange=ContentExchange@3bd9d89d=POST//localhost:9085/#WAITING(28263ms)->EXPIRED(1ms)sent=28264ms,request={"rdsrq":{"v":"4.3.0","cid":"Test_RDS_MDF1","pyld":"{\"mdfEnt\":{\"freq\":\"TICK\",\"cNm\":\"MDFServerCluster\",\"dc\":\"Test\",\"nt\":\"PREDEFINED\",\"pbDp\":5,\"rp\":2,\"rtME\":true,\"rdCfg\":false,\"sname\":\"MDF1-Test\",\"stNm\":\"market_data_stream\",\"tv\":\"testvenue\",\"valDt\":true,\"nssh\":\"main\",\"vId\":0}}","opcode":"CREATE","reqid":9489,"typ":"com.integral.virtualserver.MDFEntity"}}:Check if RDS server is running&3&Check if RDS server is running&Mon Oct 31 13:37:32 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:37:41,085  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:37:41 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:08,380  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:08 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:13,756  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:13 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:20,562  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:20 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:26,237  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:26 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:33,011  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:33 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:41,295  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:41 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:46,996  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:46 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:38:55,629  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:38:55 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:39:05,630  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:39:05 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:39:11,040  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:39:11 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
2022-10-31 13:39:17,772  INFO Test worker #[-] RDS-XX-004&RDS.NOTIFICATION.PUBLISH.FAILED&com.integral.rds.notification.server.RabbitMQNotificationServiceC&Error in setting up the Notification Publisher:Error configuring virtual host:RDS:Reason:Connection refused (Connection refused)&3&Publishing of a entity update failed; Check if RabbitMQ server is running&Mon Oct 31 13:39:17 PDT 2022&peeyushs-MacBook-Pro.local&TESTVS&null
