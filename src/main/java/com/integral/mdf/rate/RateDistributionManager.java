package com.integral.mdf.rate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import com.integral.commons.Counter;
import com.integral.mdf.RateSource;
import com.integral.mdf.multicast.MulticastRateSource;
import com.integral.mdf.rate.provisioning.ProvisioningCalculator;
import com.integral.notifications.CurrencyPairUpdate;
import com.integral.notifications.MaxCreditInfo;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.provision.MDFAggregationType;
import org.jctools.maps.NonBlockingHashMap;
import org.jctools.maps.NonBlockingHashMapLong;
import org.jctools.maps.NonBlockingHashSet;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.Util;
import com.integral.mdf.data.CreditLimitInfo;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.QuoteC;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.multicast.MulticastPriceBookSink;
import com.integral.provision.LPProvision;
import com.integral.notifications.StreamUpdate;
import static com.integral.mdf.Util.*;

public class RateDistributionManager {

    private static final int MDF_SERVER_QUOTE_VERSION = 2;
    private static final long INVALID_DEFAULT_CREDIT = -1l;

    private static final String RCM = "k=rcm";

    protected ServerProvision serverProvision;

    //index of rate channel to receives rates from differnent LPs. Indexed using stream index along with currencies
    // index.
    NonBlockingHashMapLong<RateChannel> rateChannels = new NonBlockingHashMapLong<>(1024);

    RateProcessor[] rateProcessors;

    // This will be used to assign rate processor to rate book. RateBooks will be
    // evenly distributed among rate books.
    AtomicInteger nextRateProcessorIndex = new AtomicInteger(0);

    private final Log log = LogFactory.getLog(this.getClass());

    private final int maxRateProcessorCount;

    //RateBook Index based on FI LE for credit updates.
    NonBlockingHashMapLong<List<RateBook>> creditIdxVsRateBooks = new NonBlockingHashMapLong<>(20);

    //RateBook Index based on FI and CP Indexes. it is used for credit-update/stream-update etc .
  	NonBlockingHashMapLong<RateBook> rateBooks = new NonBlockingHashMapLong<>(20);

    final boolean isUseSuperLPStreamIndex;

    NonBlockingHashSet<Integer> activeFIOrgIndices = new NonBlockingHashSet<>();

    final double creditLimitMultiplier;

    final NonBlockingHashMap<Integer,Integer> ccypRateProcessorIndex = new NonBlockingHashMap<>();

    final public UnsubscribedChannelMetrics cm;
    private RateSource rateSource;
    short USD = 1;
    int venueIndex = -1;

    public RateDistributionManager(ServerProvision serverProvision) throws IOException {
        this.serverProvision = serverProvision;
        Optional<Integer> val = serverProvision.getOrgIndex(serverProvision.getVenueName());
        this.venueIndex = val.isPresent() ? val.get() : -1;
        this.maxRateProcessorCount = serverProvision.getMaxRateProcessorCount();
        this.isUseSuperLPStreamIndex = serverProvision.isUseSuperLPStreamIndex();
        Optional<Integer> oUSD = serverProvision.getCcyIndex("USD");
        if(oUSD.isPresent()) {
            USD = oUSD.get().shortValue();
            log.info("Limit currency for max credit update is USD. index="+ USD);
        }

        this.creditLimitMultiplier = serverProvision.getCreditCutoffPercent() / 100.0;

        rateProcessors = new RateProcessor[maxRateProcessorCount];
        // 1. init rate processors.
        byte c = (byte) maxRateProcessorCount;
        do {
            byte idx = (byte) (c - 1);
            rateProcessors[c - 1] = new RateProcessor(serverProvision, idx, new MulticastPriceBookSink
                    (serverProvision));
            rateProcessors[c - 1].start();
            c--;
        } while (c > 0);
        cm = new UnsubscribedChannelMetrics("k=unsub");
        MetricsManager.instance().register(cm);
    }

    /**
     * When the FI's are getting provisioned in the server.
     * This will get called concurrently for one or more FI
     *
     * @param fiProvision the fi provisional information
     */
    public void createRateBooks(FIProvision fiProvision) {

        // 1. find the list of supported currencypair for this FI.
        Set<Integer> supportedCcyPairs = fiProvision.getSupportedCcyPairs();
        for (Integer cpIdx : supportedCcyPairs) {
            createRateBook(fiProvision,cpIdx.intValue());
        }
    }

    /**
     * Creates rate book if it doesn't exist.
     * @param fiProvision
     * @param cpIdx
     * @return
     */
    public RateBook createRateBook(FIProvision fiProvision, int cpIdx) {
        int bIdx = getBaseCurrencyIndex(cpIdx);
        int vIdx = getVarCurrencyIndex(cpIdx);

        //create or get existing rate book for FI & cpIdx
        RateBook rateBook = getOrCreateRateBook(fiProvision, cpIdx, bIdx, vIdx);

        //add for credit update index
        addToCreditIndex(fiProvision,rateBook);

        //add the rate book to every rate channels (provisioned LP's) which supports this currency pair
        addToRateChannel(fiProvision,rateBook);

        startRateSource(cpIdx);

        return rateBook;
    }


    protected void startRateSource(int cpIdx){
        Optional<String> ccyPairName = serverProvision.getCcyPairName(cpIdx);
        this.rateSource.start(ccyPairName.get());
    }

    protected void addToCreditIndex(FIProvision fiProvision,RateBook rateBook){
        List<RateBook> books = creditIdxVsRateBooks.get(fiProvision.getDefaultLEObjectId());
        if (books == null) {
            //Check if the list needs to be a concurrent data structure
            //For the same FI there wont be multiple updates here
            List<RateBook> newbooks = new ArrayList<RateBook>();
            books = creditIdxVsRateBooks.putIfAbsent(fiProvision.getDefaultLEObjectId(), newbooks);
            if (books == null) {
                books = newbooks;
            }
        }
        if(!books.contains(rateBook))
            books.add(rateBook);
    }


    protected void addToRateChannel(FIProvision fiProvision,RateBook rateBook){
        Collection<LPProvision> lpProvisions = fiProvision.getLPProvisions();
        int cpIdx = rateBook.getCcypIdx(),  bIdx = rateBook.baseCcyIdx, vIdx = rateBook.varCcyIdx;

        for (LPProvision lpProvision : lpProvisions) {
            //use streamIndex ~ objectid for key
            int streamIndex = getStreamIndex(serverProvision, lpProvision);

            if (fiProvision.isSupportedCcyPairForLP(streamIndex, cpIdx)) {
                long key = getRateChannelKey(streamIndex, bIdx, vIdx);

                RateChannel rateChannel = rateChannels.get(key);
                if (null == rateChannel) {
                    String metricsKey = getChannelMetricsKey(lpProvision, bIdx, vIdx, streamIndex);
                    RateProcessor rateProcessor = getRateProcessor(cpIdx);
                    RateChannel newRateChannel = new RateChannel(metricsKey, streamIndex, bIdx, vIdx,rateProcessor);
                    rateChannel = rateChannels.putIfAbsent(key, newRateChannel);
                    if (rateChannel == null) {
                        rateChannel = newRateChannel;
                        log.info("created new rate channel name=" + metricsKey + ", key=" + key);
                    }
                }
                //add book activates if already present.
                rateChannel.addRatebook(rateBook);
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("addToRateChannel:skipping rate book creation:" + fiProvision.getName() + "/" +
                            this.serverProvision.getCcyPairName(cpIdx).get() + "/" + lpProvision.getShortName());
                }
            }
        }
    }

    protected RateBook getOrCreateRateBook(FIProvision fi, int cpIdx, int bIdx, int vIdx) {

        long key = getRateBookKey(fi.getIndex(), bIdx, vIdx,fi.getAggregationType().getIndex());
        RateBook rateBook = rateBooks.get(key);

        //Create if there's no associated rate book for the key.
        if (rateBook == null) {
            RateBook newRateBook = newRateBook(fi,cpIdx);
            rateBook = rateBooks.putIfAbsent(key, newRateBook);
            if (rateBook == null) {
                rateBook = newRateBook;
            }
        }

        // add the FI index to the active indices
        activeFIOrgIndices.add(fi.getIndex());

        return rateBook;
    }

    public Optional<RateBook> getRateBook(int fi, int bIdx, int vIdx,int aggrType) {
        return Optional.ofNullable(rateBooks.get(getRateBookKey(fi, bIdx, vIdx, aggrType)));
    }

    private RateBook newRateBook(FIProvision fi, int cpIdx){
        RateProcessor rp = rateProcessors[getRateProcessorIndex(cpIdx)];
        if(serverProvision.isAggregationTimeSliced()){
            return new TimeSlicedRateBook(cpIdx,fi,serverProvision,rp);
        }else{
            return new RateBook(cpIdx, fi, serverProvision,rp);
        }
    }


    private String getChannelMetricsKey(LPProvision lpProvision, int bIdx, int vIdx, int streamidx) {
        StringBuilder mKey = new StringBuilder();
        mKey.append(RCM).append(", lp=").append(getLPName(lpProvision));
        mKey.append(", s=").append(lpProvision.getStreamName())
                .append(", b=").append(serverProvision.getCcyName(bIdx).get())
                .append(", v=").append(serverProvision.getCcyName(vIdx).get())
                .append(", sIdx=").append(streamidx);
        return mKey.toString();
    }

    /**
     * When the FI's are getting un-provisioned in the server.
     * This will get called concurrently for one or more FI
     *
     * @param fi the fi provisional information
     */
    public void removeRateBooks(FIProvision fi) {

        // remove the fi index from the active indices
        activeFIOrgIndices.remove(fi.getIndex());

        //these are the only ratebooks we have
        List<RateBook> rateBooks = creditIdxVsRateBooks.remove(fi.getDefaultLEObjectId());

        if(rateBooks==null) return;

        for (RateBook rateBook: rateBooks) {
            rateBook.deActivate();
            long rbKey = getRateBookKey(fi.getIndex(), rateBook.baseCcyIdx, rateBook.varCcyIdx, rateBook.aggregationType);
            this.rateBooks.remove(rbKey);
            removeBookFromChannels(rateBook,fi);
        }
    }


    public boolean deactivateRateBook(FIProvision fi, int ccypIdx, MDFAggregationType aggrType) {
        int bIdx = getBaseCurrencyIndex(ccypIdx);
        int vIdx = getVarCurrencyIndex(ccypIdx);
        Optional<RateBook> book = getRateBook(fi.getIndex(),bIdx,vIdx,aggrType.getIndex());
        if(!book.isPresent()) return false;

        //stop rate publication
        book.get().deActivate();
        //remove last cached rate
        book.get().withdrawAllLastRates();
        log.info("deactivated ratebook=" + book.get().getBookKey());
        return true;
    }


    /**
     * Removes book from all the associated rate channels.
     * @param deletedRateBook
     * @param fi
     */
    protected void removeBookFromChannels(RateBook deletedRateBook, FIProvision fi){
        //deactivate the rate book in all rate channels (provisioned LP's) which supports this currency pair
        Collection<LPProvision> lpProvisions = fi.getLPProvisions();
        for (LPProvision lpProvision : lpProvisions) {
            int streamIdx = getStreamIndex(serverProvision, lpProvision);
            long key = getRateChannelKey(streamIdx,
                    deletedRateBook.baseCcyIdx, deletedRateBook.varCcyIdx);
            RateChannel rateChannel = rateChannels.get(key);
            if (null != rateChannel) {
                ProvisioningCalculator pc = deletedRateBook.pcalcRateIndex.get(streamIdx);
                if(pc!=null)pc.unregisterMetrics();
                List<RateBook> rateBooks = rateChannel.getRateBooks();
                rateBooks.remove(deletedRateBook);
                log.info("removed rate book=" + deletedRateBook.getBookKey()+ "/"
                        +getLPStreamName(lpProvision) + " from rate channel="+ rateChannel.key);
            }
        }
    }

    public void handleStreamUpdate(UnSafeBuffer safeBuf, StreamUpdate streamUpdate) {

        streamUpdate.readFrom(safeBuf);
        if (streamUpdate.getEvent() == null) {
            log.warn("Received corrupt update:" + streamUpdate);
            return;
        }
        log.info("Received config update:" + streamUpdate);

        FIProvision fiProvision = serverProvision.getFIProvision(streamUpdate.getFiIndex());
        switch (streamUpdate.getEvent()) {
            case STREAM_SWITCH:
                if (!activeFIOrgIndices.contains(streamUpdate.getFiIndex()) || fiProvision == null || fiProvision
                        .getDefaultLEObjectId() != streamUpdate.getFiLe()) {
                    log.info("Ignoring config update:" + streamUpdate + ":Reason:FI / FI-LE not found");
                    return;
                }
                int numBooks = handleStreamSwitch(fiProvision, streamUpdate.getFromStreamIndex(), streamUpdate
                                .getToStreamIndex(), streamUpdate.getStreamStatus() == StreamUpdate
                                .STREAM_ACTIVE, streamUpdate.getStreamName(),
                        streamUpdate.getLpName());
                log.info("Successfully handled stream switch update for:" + streamUpdate + ":Updated rate books:" +
                        numBooks);
                break;
            case STREAM_STATUS_UPDATE:
                handleStreamStatusChange(streamUpdate.getFromStreamIndex(), streamUpdate.getStreamStatus(),
                        streamUpdate.getLpName());
                break;
            case LR_LP_UPDATE:
                if (!activeFIOrgIndices.contains(streamUpdate.getFiIndex()) || fiProvision == null) {
                    log.info("Ignoring config update:" + streamUpdate + ":Reason:FI not found");
                    return;
                }
                handleLiquidityRulesLPUpdate(fiProvision, streamUpdate.getFromStreamIndex(), streamUpdate
                        .getStreamStatus());
                break;
        }

    }

    public int handleLiquidityRulesLPUpdate(FIProvision fiProvision, int streamIndex, byte status) {
        int numBooksUpdated = 0;
        int processors = 0;
        List<RateBook> books = creditIdxVsRateBooks.get(fiProvision.getDefaultLEObjectId());
        if (books != null) {
            if (status == StreamUpdate.STREAM_ACTIVE) {
                for (int j = 0; j < books.size(); j++) {
                    RateBook book = books.get(j);
                    //there is nothing to aggregate, enabling pcalc for new updates
                    book.processLRLPUpdate(streamIndex, status == StreamUpdate.STREAM_ACTIVE);
                    numBooksUpdated++;
                }
            } else {
                for (int j = 0; j < books.size(); j++) {
                    RateBook book = books.get(j);
                    //withdraw old rate, trigger aggregation.
                    int rpid = book.processLRLPUpdate(streamIndex, status == StreamUpdate.STREAM_ACTIVE);
                    processors = processors | (1 << rpid);
                    numBooksUpdated++;
                }
                //3. unpark affected rate processors. This will trigger aggregation.
                unparkProcessors(processors);
            }
        }
        log.info("Successfully handled liquidity rules LP update for fi=" + fiProvision.getName() + ", numbooks=" +
                numBooksUpdated + ", status=" + status);
        return numBooksUpdated;
    }

    public void handleStreamStatusChange(int streamIndex, byte streamStatus, String lpName) {

        for (RateChannel rc : this.rateChannels.values()) {
            if (rc.getStreamIndex() == streamIndex) {
                int processors = 0;
                List<RateBook> books = rc.getRateBooks();
                if (books != null) {
                    for (int j = 0; j < books.size(); j++) {
                        RateBook book = books.get(j);
                        //withdraw old rate, trigger aggregation.
                        int rpid = book.processStreamStatusUpdate(streamIndex, streamStatus == StreamUpdate
                                .STREAM_ACTIVE, lpName);
                        if (rpid != -1) {
                            processors = processors | (1 << rpid);

                        }
                    }
                    //3. unpark affected rate processors. This will trigger aggregation.
                    if (processors != 0) {
                        unparkProcessors(processors);
                    }
                }

            }
        }
        log.info("Successfully handled stream status update for steam:" + streamIndex + ":status:" + streamStatus +
                ":lp:" + lpName);
    }


    /**
     * @param fi
     * @param fromStreamIndex
     * @param toStreamIndex
     * @param state           - updated state of stream.
     * @param streamName
     * @param lpName
     * @return return how many books were affected.
     */
    public int handleStreamSwitch(FIProvision fi, int fromStreamIndex, int toStreamIndex, boolean state, String
            streamName, String lpName) {
        int numBooks = 0;
        Optional<LPProvision> lp = findLPProvision(fi, fromStreamIndex, lpName);
        //1. update lp provision if exists. This is the relationship between FI and LP.
        if (lp.isPresent()) {
            LPProvision _lp = lp.get();
            String fromLPName = getLPStreamName(_lp);
            _lp.setStreamSuperLPIndex(toStreamIndex);
            _lp.setStreamName(streamName);
            String toLPName = getLPStreamName(_lp);
            for (RateBook book : rateBooks.values()) {
                if (book.getFIIndex() == fi.getIndex()) {
                    long fromkey = getRateChannelKey(fromStreamIndex, book.baseCcyIdx, book.varCcyIdx);
                    long tokey = getRateChannelKey(toStreamIndex, book.baseCcyIdx, book.varCcyIdx);
                    int ccpidx = getCurrencyPairIndex(book.baseCcyIdx, book.varCcyIdx);
                    String ccyp = this.serverProvision.getCcyPairName(ccpidx).get();
                    RateChannel rcFrom = rateChannels.get(fromkey);
                    RateChannel rcTo = rateChannels.get(tokey);
                    if (rcFrom != null) {
                        if (book.switchStream(fromStreamIndex, toStreamIndex, _lp, state)) {
                            rcFrom.removeRatebook(fi.getIndex(), book.getCcypIdx(),book.aggregationType);
                            if (rcTo != null) {
                                rcTo.addRatebook(book);
                                numBooks++;
                                log.info("handleStreamSwitch: switched rate book " + fi.getName() + "/" + ccyp + ", " +
                                        "from=" + fromLPName + ",to=" + toLPName + ", "
                                        + ", fromkey=" + fromkey + ", tokey=" + tokey);
                            } else {
                                //create channel
                                String metricsKey = getChannelMetricsKey(_lp, book.baseCcyIdx, book.varCcyIdx,
                                        toStreamIndex);
                                RateProcessor processor = getRateProcessor(ccpidx);
                                RateChannel newRateChannel = new RateChannel(metricsKey, toStreamIndex, book.baseCcyIdx, book.varCcyIdx,processor);
                                rcTo = rateChannels.putIfAbsent(tokey, newRateChannel);
                                if (rcTo == null) {
                                    rcTo = newRateChannel;
                                    log.info("handleStreamSwitch. created new rate channel name=" + metricsKey + ", " +
                                            "key=" + tokey);
                                }
                                rcTo.addRatebook(book);
                                log.info("handleStreamSwitch: Added rate book:" + fi.getName() + "/" + ccyp + "/" +
                                        getLPStreamName(_lp) + ",to rate channel:" + tokey);
                                numBooks++;
                            }
                        }
                    } else {
                        //This is very unlikely scenario that rate book is not associated with any rate channel
                        log.info("handleStreamSwitch: book is not associated with channel. rate book " + fi.getName() + "/" + ccyp + ", " +
                                "from=" + fromLPName + ",to=" + toLPName + ", "
                                + ", fromkey=" + fromkey + ", tokey=" + tokey);
                    }
                }
            }
        } else {
            //This is like LP is related to FI after MDF start.
            log.warn("Ignoring Stream switch:fi:" + fi.getName() + ":fromStreamIndex:" + fromStreamIndex +
                    ":toStreamIndex:" + toStreamIndex + ":state:" + state + ":name:" + streamName + ":Reason:LP " +
                    "relation not found:");
        }
        return numBooks;
    }

    protected Optional<LPProvision> findLPProvision(FIProvision fi, int fromStreamIndex, String lpName) {
        for (LPProvision lp : fi.getLPProvisions()) {
            if (lp.getShortName().equals(lpName) && lp.getStreamSuperLPIndex() == fromStreamIndex) {
                return Optional.of(lp);
            }
        }
        return Optional.empty();
    }

    protected String getLPName(LPProvision lpProvision) {
        String lpName = lpProvision.getShortName();
        if (lpProvision.getRealLP() != null && !lpProvision.getRealLP().equals(lpName)) {
            lpName = lpName + "(" + lpProvision.getRealLP() + ")";
        }
        return lpName;
    }

    protected String getLPStreamName(LPProvision lpProvision) {
        String lpName = lpProvision.getShortName();
        if (lpProvision.getRealLP() != null && !lpProvision.getRealLP().equals(lpName)) {
            lpName = lpName + "(" + lpProvision.getRealLP() + ")";
        }
        return lpProvision.getStreamName() + '@' + lpName;
    }


    RateProcessor getRateProcessor(int ccyp){
        int index = getRateProcessorIndex(ccyp);
        return rateProcessors[index];
    }

    //thread-safe
    int getRateProcessorIndex(int ccyp) {

        Integer index = ccypRateProcessorIndex.get(ccyp);
        if( index != null){
            return index.intValue();
        }

        int current = 0;
        int update = 0;
        do {
            current = nextRateProcessorIndex.get();
            update = current + 1;
            if (update >= maxRateProcessorCount) {
                update = update % maxRateProcessorCount;
            }
        } while (!nextRateProcessorIndex.compareAndSet(current, update));

        index = ccypRateProcessorIndex.putIfAbsent(ccyp,update);
        if(index==null){ //someone else inserted index against
            index = update;
        }

        log.info("RDM assigned ccyp=" + ccyp + ", rp=" + index);

        return index;
    }

    public void handleRate(QuoteC rate) {
        int version = rate.getVersion();
        if (version > MDF_SERVER_QUOTE_VERSION) {
            if (log.isDebugEnabled()) {
                log.debug("Invalid rate version recived:" + version + ", rate=" + rate.toString());
            }
            cm.recv++;cm.ver++;
            cm.recordTransmitTime(System.currentTimeMillis()-rate.getQuoteCreatedTime());
            return;
        }

        int sIdx = rate.getStreamIdx();
        int cIdx = rate.getCcyPairIdx();
        int bIdx = getBaseCurrencyIndex(cIdx);
        int vIdx = getVarCurrencyIndex(cIdx);
        long key = getRateChannelKey(sIdx, bIdx, vIdx);
        RateChannel channel = rateChannels.get(key);

        if (channel == null) {
            // channel is null
            if (log.isDebugEnabled()) {
                log.debug("Not finding the subscription/matching channel.Details are sIdx:" + sIdx + ",bccyIdx:" +
                        bIdx + ",vccyIdx:" + vIdx);
            }
            cm.recordTransmitTime(System.currentTimeMillis()-rate.getQuoteCreatedTime());
            cm.recv++;cm.unsub++;
            return;
        }

        channel.cm.recv++;

        if (!channel.isActive()) {
            channel.cm.recordTransmitTime(System.currentTimeMillis()-rate.getQuoteCreatedTime());
            channel.cm.inactive++;
            return;
        }

        rate.stampReceivedTime();
        try {
            int rpids = channel.receiveRate(rate);
            //un-park rate processors
            unparkProcessors(1<<rpids);
            channel.cm.recordTransmitTime((rate.getReceivedTime().toEpochMilli()-rate.getQuoteCreatedTime())*10);//1 ms buckets
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("unable to handle rate message. sIdx:" + sIdx + ",bccyIdx:" + bIdx + ",vccyIdx:" + vIdx +
                        ",key=" + key);
            }
        }
    }

    public void unparkProcessors(int rpids) {
        //un-park rate processors
        for (int i = 0; i < maxRateProcessorCount; i++) {
            if ((rpids & (1 << i)) > 0) {
                rateProcessors[i].unpark();
            }
        }
    }

    public void handleCreditUpdate(UnSafeBuffer safeBuf, CreditLimitInfo creditLimitInfo) {

        //represents version byte
        //safeBuf.get();
        //represents message type if filtering is required for message type , this will be used
        //safeBuf.getShort();
        double totalSize = safeBuf.limit();//1400

        //Number of lines for which there are credit updates. 
        int linesSize = (int) totalSize / CreditLimitInfo.MSG_SIZE;

        //Read into creditLimitInfo
        for (int i = 0; i < linesSize; i++) {
            //Re-using the object instead of creating multiple and throwing away , which creates so much clutter.
            //read only FI and LP LE , to do quick check and drop
            creditLimitInfo.readOnlyLEInfo(safeBuf);
            long fiLe = creditLimitInfo.getFiLe();
            List<RateBook> books = creditIdxVsRateBooks.get(fiLe);
            RateBook rateBook;
            if (books != null) {
                for (int j = 0; j < books.size(); j++) {
                    rateBook = books.get(j);
                    if (!rateBook.doesCalculatorExist()) {
                        if (log.isDebugEnabled()) {
                            log.debug("No calculator exist for FILE:" + creditLimitInfo.getFiLe() + ",LPLE:" +
                                    creditLimitInfo.getLpLe());
                        }
                        continue;
                    }

                    if (creditLimitInfo.isPartiallyRead(safeBuf)) {
                        creditLimitInfo.readAfterLEInfo(safeBuf);
                        //pre-process credit limits
                        if (creditLimitMultiplier > 0) {
                            if (creditLimitInfo.getAggregateAvailable() != INVALID_DEFAULT_CREDIT) {
                                double lmt = creditLimitInfo.getAggregateAvailable() - creditLimitInfo
                                        .getAggregateLimit() * creditLimitMultiplier;
                                lmt = lmt < 0.0 ? 0.0d : lmt;
                                creditLimitInfo.setAggregateAvailable((long) lmt);
                            }
                            if (creditLimitInfo.getDailyAvailable() != INVALID_DEFAULT_CREDIT) {
                                double lmt = creditLimitInfo.getDailyAvailable() - creditLimitInfo.getDailyLimit() *
                                        creditLimitMultiplier;
                                lmt = lmt < 0.0 ? 0.0d : lmt;
                                creditLimitInfo.setDailyAvailable((long) lmt);
                            }
                        }
                    }
                    rateBook.handleCreditUpdate(creditLimitInfo);
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("No books for FILE :" + fiLe);
                }
            }
            //Skip to next line if the buffer was read partially.
            creditLimitInfo.readFully(safeBuf);
        }
    }

    public boolean handleMaxCredit(UnSafeBuffer safeBuf, MaxCreditInfo maxCreditInfo) {

        //represents version byte
        //safeBuf.get();
        //represents message type if filtering is required for message type , this will be used
        //safeBuf.getShort();
        double totalSize = safeBuf.limit();//1400

        //Number of lines for which there are credit updates.
        int linesSize = (int) totalSize / MaxCreditInfo.MSG_SIZE;

        //Read into maxCreditInfo
        for (int i = 0; i < linesSize; i++) {
            //Re-using the object instead of creating multiple and throwing away , which creates so much clutter.
            //read only FI and LP LE , to do quick check and drop
            maxCreditInfo.readFrom(safeBuf);
            if(maxCreditInfo.getVenue() != venueIndex && venueIndex != -1 ){
                if(log.isDebugEnabled()){
                   log.debug("venue index is not a match. MDF venueIndex="
                           + venueIndex+", credit message venueIndex="+maxCreditInfo.getVenue());
                }
                return false;
            }

            long fiLe = maxCreditInfo.getFiLE();
            if(fiLe==0){
                if(log.isDebugEnabled())
                    log.debug("rdm.handleMaxCredit <eom>.");
                break;
            }
            List<RateBook> books = creditIdxVsRateBooks.get(fiLe);
            RateBook rateBook;
            if (books != null) {
                for (int j = 0; j < books.size(); j++) {
                    rateBook = books.get(j);
                    //make sure book isn't empty. And there are LPs
                    if (!rateBook.doesCalculatorExist()) {
                        if (log.isDebugEnabled()) {
                            log.debug("rdm.mci No calculator exist for FILE:" + maxCreditInfo.getFiLE() + ",venue:" +
                                    maxCreditInfo.getVenue());
                        }
                        continue;
                    }

                    //pre-process credit limits
                    long limit = maxCreditInfo.getMaxCredit();
                    byte status = (byte)1; // 1 - credit enabled
                    if (maxCreditInfo.getMaxCredit() <= 1) {
                        limit=0;
                        status=0;//no credit
                    }

                    rateBook.handleCreditUpdate(maxCreditInfo.getValueDate(),
                            status,
                            limit,
                            USD);
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("rdm.mci No books for FILE :" + fiLe);
                }
            }
        }
        return true;
    }

    public void onCloseHeartbeat(int ccyPairIndex) {
        log.info("rdm.onCloseHeartbeat start, ccyPairIndex=" + ccyPairIndex);

        //1. inactivate rate channel for live updates
        for (RateChannel rc : this.rateChannels.values()) {
            if (rc.getCcypIdx() == ccyPairIndex) {
                rc.inactivate();
            }
        }

        //2. withdraw rate from affected books.
        int processors = 0;
        for (RateBook aBook : rateBooks.values()) {
            if (aBook.getCcypIdx() == ccyPairIndex) {
                if (aBook.isActive()) {
                    int rpId = aBook.withdrawAllLastRates();
                    processors = processors | (1 << rpId);
                } else {
                    aBook.bm.inactDrp++;
                }
            }
        }

        //3. unpark affected rate processors. This will trigger aggregation.
        unparkProcessors(processors);

        log.info("rdm.onCloseHeartbeat end, ccyPairIndex=" + ccyPairIndex);
    }

    public void onOpenHeartbeat(int ccyPairIndex) {

        //1. activate ratechannels again.
        for (RateChannel rc : this.rateChannels.values()) {
            if (rc.getCcypIdx() == ccyPairIndex) {
                rc.activate();
            }
        }
        log.debug("rdm.onOpenHeartbeat, ccyPairIndex=" + ccyPairIndex);
    }

    public void onHeartbeatTimeout() {
        log.info("rdm.onHeartbeatTimeout start");

        //1. inactivate rate channel for live updates
        for (RateChannel rc : this.rateChannels.values()) {
            rc.inactivate();
        }

        //2. withdraw rate from book.
        int processors = 0;
        for (RateBook aBook : rateBooks.values()) {
            if (aBook.isActive()) {
                int rpId = aBook.withdrawAllLastRates();
                processors = processors | (1 << rpId);
            } else {
                aBook.bm.inactDrp++;
            }
        }

        //3. unpark affected rate processors. This will trigger aggregation.
        unparkProcessors(processors);

//        //4. activate ratechannels again.
//        for (RateChannel rc : this.rateChannels.values()) {
//            rc.activate();
//        }

        log.info("rdm.onHeartbeatTimeout end");
    }

    public void setRateSource(RateSource rateSource) {
        this.rateSource = rateSource;
    }

    /**
     * Remove ratebook which matches given parameters.
     * @param fi
     * @param ccypIdx
     * @param aggregationType
     */
    public void removeRateBook(FIProvision fi, int ccypIdx, MDFAggregationType aggregationType) {
        int bIdx = getBaseCurrencyIndex(ccypIdx);
        int vIdx = getVarCurrencyIndex(ccypIdx);
        long rbKey = getRateBookKey(fi.getIndex(), bIdx, vIdx, aggregationType.getIndex());
        RateBook book = rateBooks.get(rbKey);
        if(book!=null) {
            this.rateBooks.remove(rbKey);
            MetricsManager.instance().unregister(book.bm);
            removeBookFromChannels(book,fi);
        }else {
            log.info("removeRateBook ");
        }
    }

    public void handleCurrencyPairUpdate(UnSafeBuffer safeBuf, CurrencyPairUpdate currencyPairUpdate) {
        currencyPairUpdate.readFrom(safeBuf);
        if(currencyPairUpdate.getEvent())
    }

    public static class UnsubscribedChannelMetrics implements Metrics {
        public int recv;
        public int ver;
        public int unsub;
        private String mKey;
        final Counter trsmtime = new Counter("trsm");
        NonBlockingHashMapLong keys = new NonBlockingHashMapLong();
        Log log = LogFactory.getLog(this.getClass());

        public UnsubscribedChannelMetrics(String mKey) {
            this.mKey =mKey;
        }

        public void recordTransmitTime(long l) {
            trsmtime.record(l);
        }

        @Override
        public StringBuilder report() {
            StringBuilder message = new StringBuilder(64);
            message.append(mKey);
            message.append(", rec=").append(recv);
            message.append(", ver=").append(ver);
            message.append(", unsub=").append(unsub);
            message.append(", ").append(trsmtime.toString());
            message.append(", keys=").append(keys.values());

            this.ver =0;
            this.recv =0;
            this.unsub =0;
            trsmtime.reset();

            log.info("unsubscribed keys = "+ keys);
            keys.clear();

            return message;
        }
    }

}