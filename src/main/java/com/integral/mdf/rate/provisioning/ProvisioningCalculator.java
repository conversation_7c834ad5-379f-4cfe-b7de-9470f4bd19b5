package com.integral.mdf.rate.provisioning;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import com.integral.commons.pool.ObjectPool;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.CreditLimitInfo;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ProvisionedQuoteC;
import com.integral.mdf.data.QuoteC;
import com.integral.mdf.rate.live.RateConversionCache;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.provision.LPProvision;
import com.integral.provision.SpreadRuleParameterProvision;
import com.integral.util.MathUtil;
import com.integral.util.Tuple;

public class ProvisioningCalculator {
	
	private static final long INVALID_DEFAULT_CREDIT = -1l;
	private static final byte LRLP_UPDATE_BIT = 0;
	private static final byte STREAM_STATUS_BIT = 1;

	final AtomicReference<QuoteC> rawRate = new AtomicReference<QuoteC>(null);
	
	RateMetrics rm;

	private ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
	
	private  RateConversionCache rateConversion = RateConversionCache.getInstance();
	
	AtomicBoolean isCreditModified = new AtomicBoolean(false);
	
	//Only credit thread updates this . Its a single thread listener on multicast 
	Map<Short,Tuple<Boolean,Long>> creditInfo = new HashMap<Short,Tuple<Boolean,Long>>();
	
	final int baseccyIdx;
	final int varCcyIdx;
	final ObjectPool<QuoteC> rawRatePool;
	
	private final Log log = LogFactory.getLog(this.getClass());
	
	private String logKey;
	
	private int precision;

	// each bit is used to set/unset a config change as follows:
	// 0th bit - LR_LP_UPDATE; set to 0 when LP is disabled in Liquidity rules
	// 1st bit - STREAM_STATUS_CHANGE; set to 0 when stream is made inactive
	private byte active = (byte) 0x7F;

	private final boolean validateValueDate;

	private final String lp;
	
	private final List<SpreadRuleParameterProvision> spreads;
	
	private boolean isApplySpreads;
	
	double bidpips = 0.0d, offerpips = 0.0d;
	double bidbps = 0.0d, offerbps = 0.0d;
	
	public final static int PIPS = 0;
	public final static int BPS = 1;
	protected final long BASISDIVISORFACTOR = 10000;
	
	public ProvisioningCalculator(String mKey , String fiKey, LPProvision lp, FIProvision fiProvision, int baseCcyIdx,
			int varCcyIdx, ObjectPool<QuoteC> rawRatePool,int precision, int lpIdx, boolean validateValueDate) {
		this.baseccyIdx = baseCcyIdx;
		this.varCcyIdx = varCcyIdx;
		this.lp = lp.getShortName();
		//Spreads will be applied to the rate
		//this.spreads = fiProvision.getSpreads(lp.getStreamSuperLPIndex(), baseccyIdx, varccyIndex);
		String logKey = getLogKey(lp);
		provisionedQuote.setKey(logKey);
		provisionedQuote.setLPIndex(lpIdx);
		this.logKey = logKey + fiKey;
		this.rm= new RateMetrics(mKey);
		MetricsManager.instance().register(rm);
		this.rawRatePool = rawRatePool;
		this.precision = precision;
		this.validateValueDate = validateValueDate;
		spreads = fiProvision.getSpreads(lp.getStreamSuperLPIndex(), baseCcyIdx, varCcyIdx);
		for (SpreadRuleParameterProvision srp : spreads) {
			isApplySpreads = isApplySpreads || ( srp.isEnabled() && srp.isPreTradeEnabled() );
			if( srp.isEnabled() && srp.isPreTradeEnabled() ){
				if(srp.getSpreadType() == PIPS){
					bidpips = bidpips + srp.getRawbidspread();
					offerpips = offerpips + srp.getRawofferspread();
				}else if( srp.getSpreadType() == BPS ){
					bidbps = bidbps + srp.getRawbidspread();
					offerbps = offerbps + srp.getRawofferspread();
				}
			}
		}
		if(log.isInfoEnabled()){
			log.info("calc=" +this.logKey + " spreads=[bpips="+bidpips+",opips="+offerpips+",bbps="+bidbps+",obps="+offerbps+"]");
		}
	}
	
	/**
	 * Update logging messages
	 * @param lp
	 * @param mKey
	 * @param fiKey
	 */
	public void onStreamSwitch(LPProvision lp,String mKey,String fiKey, boolean state){
		MetricsManager.instance().unregister(this.rm);
		withdrawRate();
		if(state){
			activateForStreamStatusUpdate();
		}else{
			deActivateForStreamStatusUpdate();
		}
		String logKey = getLogKey(lp);
		provisionedQuote.setKey(logKey);
		this.logKey = logKey + fiKey;
		this.rm= new RateMetrics(mKey);
		MetricsManager.instance().register(rm);
	}

	/**
	 * Called by raw lp rate i/O thread. Keep processing minimum and lock free.
	 * @param rate
	 * @return
	 */
	public boolean onRate(QuoteC rate) {
		if (isActive()) {
			rm.rawrecv++;
			QuoteC old = rawRate.getAndSet(rate);
			if (old != null) {
				//Return the original quote back to pool
				rawRatePool.returnObject(old);
				rm.rawdropped++;
			}
			return true;
		} else {
			QuoteC old = rawRate.getAndSet(null);
			if (old != null) {
				//Return the original quote back to pool
				rawRatePool.returnObject(old);
				rm.rawdropped++;
			}
			rm.incdrp++;
			return false;
		}
	}
	
	public void withdrawRate() {
		QuoteC old = rawRate.getAndSet(null);
		if(old != null){
			// this is unusual for withdraw rate. Ideally nothing should be cached, 
			// should aggregated as soon as it arrives.
			
			//Return the original quote back to pool
			rawRatePool.returnObject(old);
			rm.wdr++;
		}
		this.provisionedQuote.setActive(false);
		this.provisionedQuote.resetBuffer();
	}

	public boolean isActive() {
		return active == 0x7F;
	}

	public String getLp() {
		return lp;
	}

	// for test cases
	public byte getActive() {
		return active;
	}

	public void activateForLRLPUpdate() {
		active |= 1 << LRLP_UPDATE_BIT;
	}

	public void deActivateForLRLPUpdate() {
		active &= ~(1 << LRLP_UPDATE_BIT);
	}

	public void activateForStreamStatusUpdate() {
		active |= 1 << STREAM_STATUS_BIT;
	}

	public void deActivateForStreamStatusUpdate() {
		active &= ~(1 << STREAM_STATUS_BIT);
	}
	
	public ProvisionedQuoteC getProvisionedRate(){
		
		QuoteC quote = rawRate.getAndSet(null);
		if(quote!=null){
			try{
				//provision it and cache it
				//provisioned quote refreshed from last orgnl quote, since it will be modified
				this.provisionedQuote.readFrom(quote);
				
				//Take the credit corresponding to the value date of the quote 
				Tuple<Boolean, Long> credit = creditInfo.get(this.provisionedQuote.getValueDate());
				if(null != credit && credit.first){
					//Apply credit and update provisioned quote.
					applyCredit(this.provisionedQuote,credit);
					rm.creditApplied++;
				}
				else{
					this.provisionedQuote.copyAllTiersToProvisionTiers();
				}
				
				provisionRate();
				
				if(isQuoteInActive()){
					provisionedQuote.setActive(false);
				}else{
					//since provisionedQuote is cached , this denotes there's rate update for the book , looked upon by aggregation   
					provisionedQuote.setActive(true);
				}
			} finally {
				//Return the original quote back to pool 
				rawRatePool.returnObject(quote);
			}
			rm.provisioned++;
		}else if(isCreditModified.compareAndSet(true, false)){
			// CAS : this means credit is modified and needs to be re-applied 
			
			//Take the credit corresponding to the value date of the quote 
			Tuple<Boolean, Long> credit = creditInfo.get(this.provisionedQuote.getValueDate());
			if(null != credit && credit.first){
				applyCredit(this.provisionedQuote, credit);
				rm.creditApplied++;
			}else{
				this.provisionedQuote.copyAllTiersToProvisionTiers();
			}

			provisionRate();
			
			if(isQuoteInActive()){
				provisionedQuote.setActive(false);
			}else{
				//since provisionedQuote is cached , this denotes there's rate update for the book , looked upon by aggregation   
				provisionedQuote.setActive(true);
			}
			rm.creditModified++;
		}
		
		//return provisioned/cached value
		return provisionedQuote;
	}
	
	private void provisionRate() {
		int pBidTiersNum = provisionedQuote.getPBidTiersNum();
		int pOfferTiersNum = provisionedQuote.getPOfferTiersNum();
		
		for (int i=0;i<pBidTiersNum ;i++) {
			int offset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, i);
			double rate = provisionedQuote.getPPrice(offset);
			double bpspread = (bidbps*rate)/BASISDIVISORFACTOR;
			rate = rate - bidpips - bpspread;
			provisionedQuote.setPPrice(offset,MathUtil.round(rate,precision,BigDecimal.ROUND_FLOOR));
		}
		
		for (int i=0;i<pOfferTiersNum ;i++) {
			int offset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, i);
			double rate = provisionedQuote.getPPrice(offset);
			double bpspread = (offerbps*rate)/BASISDIVISORFACTOR;
			rate = rate + offerpips + bpspread;
			provisionedQuote.setPPrice(offset, MathUtil.round(rate,precision,BigDecimal.ROUND_CEILING));
		}
	}

	private boolean isQuoteInActive() {
		
		if(validateValueDate  && this.provisionedQuote.getValueDate() <=0){
			rm.inactive[3]++;
			return true;
		}
		
		
		int pBidTiersNum = this.provisionedQuote.getPBidTiersNum();
		int pOfferTiersNum = this.provisionedQuote.getPOfferTiersNum();
		if(pBidTiersNum < 1 &&  pOfferTiersNum < 1){
			rm.inactive[0]++;
			return true;
		}

		//If limits are zero mark it inactive
		if(pBidTiersNum >= 1 && this.provisionedQuote.getPShowQty(this.provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0)) == 0.00
				&& pOfferTiersNum >= 1 && this.provisionedQuote.getPShowQty(this.provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0)) == 0.00 ){
			rm.inactive[1]++;
			return true;
		}
		
		//If rates are zero mark it inactive
		if(pBidTiersNum >= 1 && this.provisionedQuote.getPPrice(this.provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0)) == 0.00
				&& pOfferTiersNum >= 1 && this.provisionedQuote.getPPrice(this.provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0)) == 0.00 ){
			rm.inactive[2]++;
			return true;
		}
				
		return false;
	}

	public void applyCredit(ProvisionedQuoteC provQuote,Tuple<Boolean, Long> credit) {
	
		long creditLimit =  credit.second;		
		boolean isMultiQuote = provQuote.isMultiQuote();
		int noOfBidTiers = provQuote.getBidTiersNum();		
		long maxBidCreditLimit = creditLimit;
		int fBidTiers = noOfBidTiers;
		
		for (int i=0; i<fBidTiers ; i++){			
			//Do bid side processing  // Tiers start @ 1 
			int bTierOffset = provQuote.tierOffset(ProvisionedQuoteC.BUY, i);				
			double bAmt = provQuote.getTotalQty(bTierOffset);
			
			//If limit breached then the rest of the tiers are untouched 
			//Use the total tier numbers to understand how much to iterate 
			if (bAmt >= maxBidCreditLimit) {
            	bAmt = maxBidCreditLimit;
            	fBidTiers = i+1;
            	provQuote.copyTierToProvisionTier(ProvisionedQuoteC.BUY,i);
            	provQuote.setPShowQty(provQuote.pTierOffset(ProvisionedQuoteC.BUY, i), bAmt);
            	//provQuote.setPTotalQty(provQuote.pTierOffset(ProvisionedQuoteC.BUY, i+1), bAmt);
            }else{
            	//Copy the entire tier data to provisioned tier
            	provQuote.copyTierToProvisionTier(ProvisionedQuoteC.BUY,i);
            }
	
            if (isMultiQuote) {
            	maxBidCreditLimit -= bAmt;
            }
		}
		//WE HAVE TO SET THIS SO THAT THE OFFER TIERS OFFSET IS RIGHTLY CALCULATED for future write's into buffer.
		provQuote.setPBidTiersNum(fBidTiers);
		
		int noOfOfferTiers = provQuote.getOfferTiersNum();		
		long maxOfferCreditLimit = creditLimit;
		int fOfferTiers = noOfOfferTiers;
		
		for (int i=0; i<fOfferTiers ; i++){
			//Do offer side processing  //Tiers start @ 1				
			int oTierOffset = provQuote.tierOffset(ProvisionedQuoteC.SELL, i);
			double oAmt  = provQuote.getTotalQty(oTierOffset);
			
			//If limit breached then the rest of the tiers are untouched 
			//Use the total tier numbers to understand how much to iterate and receive 
			if (oAmt >= maxOfferCreditLimit) {
                oAmt = maxOfferCreditLimit;
                fOfferTiers = i+1;
                provQuote.copyTierToProvisionTier(ProvisionedQuoteC.SELL,i);
                provQuote.setPShowQty(provQuote.pTierOffset(ProvisionedQuoteC.SELL, i), oAmt);
            	//provQuote.setPTotalQty(provQuote.pTierOffset(ProvisionedQuoteC.SELL, i+1), oAmt);
            }else{
            	//Copy the entire tier data to provisioned tier
            	provQuote.copyTierToProvisionTier(ProvisionedQuoteC.SELL,i);
            }
			
            if (isMultiQuote) {
                maxOfferCreditLimit -= oAmt;
            }
		}
		provQuote.setPOfferTiersNum(fOfferTiers);
	}

	public void onCredit(CreditLimitInfo cl) {
		long creditLimit = getCreditLimit(cl);
		onCredit(cl.getValueDate(),cl.getCreditStatus(),creditLimit,cl.getLimitCcy());
	}

	public void onCredit(short valueDate,byte status,long creditLimit, short limitccy ){
		
		//Handle current value date here
		boolean isCreditEnabledCurrent =  status == (byte)1 ? true :false;
		
		Tuple<Boolean, Long> tuple = creditInfo.get(valueDate);
		if(tuple == null){
			tuple = new Tuple<Boolean, Long>();
			tuple.first = false;
			tuple.second = 0l;		
			creditInfo.put(valueDate, tuple);
		}
		
		if(isCreditEnabledCurrent){
			long newCreditLimit = getCreditLimitInBaseCcy(creditLimit, limitccy);
			if(tuple.second == newCreditLimit && isCreditEnabledCurrent == tuple.first){
				//No change to credit 
				if(log.isDebugEnabled()){
					log.debug(this.logKey+valueDate+"|"+tuple.first+"|"+tuple.second+"CreditNoChange");
				}
				return;
			}
			tuple.first = true;
			tuple.second = newCreditLimit;			
			//Use this as a memory barrier and flush.
			isCreditModified.set(true);
			
			if(log.isDebugEnabled()){
				log.debug(this.logKey+valueDate+"|"+tuple.first+"|"+tuple.second+"|CreditUpdated");
			}
		}else if(isCreditEnabledCurrent != tuple.first){
				//Change in state so flush 
				tuple.first = isCreditEnabledCurrent; 
				//Use this as a memory barrier and flush.	
				isCreditModified.set(true);
				if(log.isDebugEnabled()){
					log.debug(this.logKey+valueDate+"|"+tuple.first+"|"+tuple.second+"|CreditStatusChangedToCreditNotEnabled");
				}
		}else{
			if(log.isDebugEnabled()){
				log.debug(this.logKey+valueDate+"|"+tuple.first+"|"+tuple.second+"|CreditNotEnabled");
			}
		}
	}

	protected long getCreditLimit(CreditLimitInfo cl) {
		if(cl.getDailyAvailable() == INVALID_DEFAULT_CREDIT){
			return cl.getAggregateAvailable();
		}else if(cl.getAggregateAvailable() == INVALID_DEFAULT_CREDIT ){
			return cl.getDailyAvailable();
		}
		return Math.min(cl.getDailyAvailable(), cl.getAggregateAvailable());
	}
	
	private long getCreditLimitInBaseCcy(long aggregateLimit, int limitCcy) {		
		if (baseccyIdx == limitCcy){
			return aggregateLimit;
		}else{
			//Convert using live rate .Create a cache for conversion.
			Optional<Double> rate = rateConversion.getRate(limitCcy, baseccyIdx);
			if(rate.isPresent()){
				Double value = rate.get();
				//TODO :Check the down cast to long
				return (long) (aggregateLimit * value);
			}else{
				if(limitCcy>=1)
					log.info("PC.1 conversion rate not available baseccy="+baseccyIdx+", limitccy="+limitCcy );
				else
					if(log.isDebugEnabled())
						log.debug("PC.1 conversion rate not available baseccy="+baseccyIdx+", limitccy="+limitCcy );
				return aggregateLimit;
			}
		}
	}
	
	private String getLogKey(LPProvision lp) {
		StringBuilder lKey = new StringBuilder();
		lKey.append(getLPName(lp)).append('|')
			.append(lp.getStreamName()).append('|');
		return lKey.toString();
	}
	
	protected String getLPName(LPProvision lpProvision) {
		String lpName = lpProvision.getShortName();
		if(lpProvision.getRealLP()!=null && !lpProvision.getRealLP().equals(lpName)){
			lpName = lpName +"("+lpProvision.getRealLP()+")";
		}
		return lpName;
	}

	public void unregisterMetrics() {
		MetricsManager.instance().unregister(this.rm);
	}

	public class RateMetrics implements Metrics {
		
		public int rawrecv;
		public final int[] inactive = {0,0,0,0};
		public int rawdropped;
		public int provisioned;
		public int creditApplied;
		public int creditModified;
		public int wdr;
		public int incdrp;
		private String mKey;

		public RateMetrics(String mKey) {
			this.mKey = mKey; 
		}

		@Override
		public StringBuilder report() {
			StringBuilder message = new StringBuilder(120);
			message.append(mKey);
			message.append(", rec=").append(rawrecv);
			message.append(", prc=").append(provisioned);
			message.append(", ca=").append(creditApplied);
			message.append(", cm=").append(creditModified);
			message.append(", drp=").append(rawdropped);
			message.append(", wdr=").append(wdr);
			message.append(", lpina=").append(incdrp);
			message.append(", ina=").append(Arrays.toString(inactive));
			
			this.rawrecv = 0;
			this.provisioned = 0;
			this.creditApplied = 0;
			this.creditModified = 0;
			this.rawdropped = 0;
			this.wdr = 0;
			this.incdrp = 0;
			Arrays.fill(this.inactive,0);
			return message;
		}
	}

}
