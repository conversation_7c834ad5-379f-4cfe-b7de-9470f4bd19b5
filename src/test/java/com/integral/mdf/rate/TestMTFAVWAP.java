package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestMTFAVWAP extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";

    protected void set3TierRates(QuoteC quote,byte buyOrSell,double p1, double p2, double p3){
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
        //tier 3
        tieridx = quote.tierOffset(buyOrSell, 2);
        quote.setPrice(tieridx, p3);
    }

    /**
     * MultiQuote-MultiQuote VWAP aggregation.
     * @throws Exception
     PLT-3520*/
    @Test
    public void testMTFAVWAPromMultiQuoteLPs () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
      //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
      //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
   //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs =====");
    }


    /**
     * MultiTier-MultiQuote VWAP aggregation.
     * @throws Exception
     */
    @Test
    public void testVWAPMQMTLP () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPMQMTLP =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPMQMTLP ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
  //      assertEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
 //       assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
 //       assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21227, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
 //       assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPMQMTLP =====");
    }


    @Test
    public void testMTFAVWAPromMultiQuoteLPs_smalltiers () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_smalltiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{500.00,1500.00,2000.00});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_smalltiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
      //  assertEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        //delta value has to be more to validate all the precisions, otherwise it will round off and the assertion will pass even if value is wrong
       // assertEquals(1.2121, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
    //    assertEquals(500, aggregate.getBidQtys()[0], 0.01);
    //    assertEquals(1500, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
     //   assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
     //   assertEquals(1.2122, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
    //    assertEquals(500, aggregate.getOfferQtys()[0], 0.01);
    //    assertEquals(1500, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_smalltiers =====");
    }


    @Test
    public void testMTFAVWAPromMultiQuoteLPs_bigtiers () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_bigtiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{3000,4000,5000,8000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_bigtiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21206, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        //delta value has to be more to validate all the precisions, otherwise it will round off and the assertion will pass even if value is wrong
        assertEquals(1.21202, aggregate.getBidPrices()[2], 0.00001);
        //1.211975 is the full value, gets rounded down to 5 precision
        assertEquals(1.21197, aggregate.getBidPrices()[3], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21224, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21225, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.21228, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21234, aggregate.getOfferPrices()[3], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(3000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_bigtiers =====");
    }

    @Test       //scenario - Tier values are too high and LP quotes do not have that much liquidity, then quote gets dropped in mdf PLT-3520
    public void testMTFAVWAPromMultiQuoteLPs_noliquidity () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_noliquidity =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{12000,13000,15000,18000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_noliquidity ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21192, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(12000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21245, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(12000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_noliquidity =====");
    }


    @Test
    public void testMTFAVWAPromMTMQTierLimitEQAvblQuoteLimit() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testMTFAVWAPromMTMQTierLimitEQAvblQuoteLimit =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000,7000,9000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_bigtiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
//        assertEquals(1.21210, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.21194, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21191, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.21188, aggregate.getBidPrices()[5], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
//        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(7000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(9000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
    //    assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212266, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212350, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.212380, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.212442, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.212500, aggregate.getOfferPrices()[5], 0.00001);

        //The book is sorted at the offer side as well
//        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMTMQTierLimitEQAvblQuoteLimit =====");
    }

    @Test   //available is 12k,, so last tier shud get 12k price  - lasttierGRlmt PLT-3520
    public void testMTFAVWAPromMultiQuoteLPs_alltiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_alltiers  =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500.00,2500.00,3000.00,4000.00,5000.00,8000.00,10000.00,13000.00});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_alltiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21208, aggregate.getBidPrices()[1], 0.00001);
        //delta value has to be more to validate all the precisions, otherwise it will round off and the assertion will pass even if value is wrong
        assertEquals(1.212066, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21202, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.211975, aggregate.getBidPrices()[5], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[6], 0.00001);
        assertEquals(1.211925, aggregate.getBidPrices()[7], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(8000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(10000, aggregate.getBidQtys()[6], 0.01);
        assertEquals(12000, aggregate.getBidQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumBids());

        //1.2122, 1.21222, 1.212234, 1.21225, 1.21228, 1.212338, 1.21239
        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21222, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212234, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21225, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.21228, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.212338, aggregate.getOfferPrices()[5], 0.00001);
        assertEquals(1.21239, aggregate.getOfferPrices()[6], 0.00001);
        assertEquals(1.212442, aggregate.getOfferPrices()[7], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(1500, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(8000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(10000, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(12000, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_alltiers =====");


    }


    @Test
    public void testMTFAVWAPromMultiQuoteLPs_SQ () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_SQ =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_SQ ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_SQ =====");
    }

    @Test
    public void testMTFAVWAPromMultiQuoteLPs_SQsingleTier () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_SQLPsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_SQLPsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_SQLPsingleTier =====");
    }

    @Test
    public void testVWAPMultiTier_Basic () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiTierLPs_SQsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");
    }

    @Test
    public void testMultiTier_SecondTier () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiTierLPs_SQsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2120, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2123, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");
    }

    @Test
    public void testMultiQuote () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiTierLPs_SQsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21205, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21225, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");
    }

    @Test //MTMQ
    public void testMTFAVWAPromMultiTierLPs_SQsingleTier () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiTierLPs_SQsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiTierLPs_SQsingleTier =====");
    }


    @Test
    public void testMTFAVWAPromLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testMTFAVWAPromLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20,2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromLPCausingInvertedTiers ==="+aggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.1866, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.00001);
        //Offer is doing round down instead of round up, hence this assert fails
        assertEquals(1.20333, aggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromLPCausingInvertedTiers =====");

    }


    @Test //lower precision hence passes
    public void testMTFAVWAPromMTLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testMTFAVWAPromMTLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20,2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMTLPCausingInvertedTiers ==="+aggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.18, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMTLPCausingInvertedTiers =====");

    }


    @Test
    public void testMTFAVWAPromLPGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : testMTFAVWAPromLpGivingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromSQLPGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAVWAPromLPGivingInvertedTiers =====");

    }


    @Test  //aggregation not happening for one-sided rates  PLT-3525 fixed
    public void testMTFAVWAPromMultiQuoteLPs_BidSideOnly () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_BidSideOnly =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        //set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, true);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        //set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_BidSideOnly ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212066, aggregate.getBidPrices()[1], 0.000001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_BidSideOnly =====");
    }


    @Test  //fails should give quote for 6k, since 6k is available after stream1 inactive PLT-3520
    public void testMTFAVWAPromMTMQ_StreamIna() throws Exception {
        System.out.println("===== START : testMTFAVWAPromMTMQ_StreamIna =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000,7000,9000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMTMQ_StreamIna ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
       // assertEquals(1.21210, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.21194, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21191, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.21188, aggregate.getBidPrices()[5], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        //assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(7000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(9000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
//        assertEquals(1.212200, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212266, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212350, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.212380, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.212442, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.212500, aggregate.getOfferPrices()[5], 0.00001);

        //The book is sorted at the offer side as well
//        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());


        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======book1 when stream1 inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.211966, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.211925, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.2119, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.211883, aggregate.getBidPrices()[5], 0.00001); //1.211833

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(6000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21235, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.2124, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.212475, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.21252, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.21255, aggregate.getOfferPrices()[5], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(6000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAVWAPromMTMQ_StreamIna =====");
    }

    @Test
    public void testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2121,0,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.21);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2121,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2121, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //Test agg after second quote
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.23);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2123,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====second aggregate quote in testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212167, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());



        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer =====");
    }

    @Test  //aggregated book has single tier
    public void testMTFAVWAPromMTMQLPs_samebidoffer1T2Lps () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testMTFAVWAPromMTMQLPs_samebidoffer1T2Lps =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2123,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2123,1.2120,1.2119);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2121,1.2119,1.2118);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMTMQLPs_samebidoffer1T2Lps ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferPrices()[1], 0.01);
        assertNotEquals(1.21224, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs =====");
    }

    @Test //aggregation gives 0 - LP1 giving same rates for T1 and T2
    public void testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1Lp() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1Lp =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2121,1.2121);
        set3TierRates(quote,QuoteC.SELL,1.2121,1.2121,1.2121);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1Lp ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(3000.0, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1.2121, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(3000.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1Lp =====");
    }

    @Test //aggregated book gets merged
    public void testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1LpMerge() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1LpMerge =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2121,0);
        set3TierRates(quote,QuoteC.SELL,1.2121,1.2121,0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

//        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.21);
//        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
//        set3TierRates(quote,QuoteC.SELL,1.2121,0,0);
//        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
//
//        manager.handleRate(quote);
//        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1LpMerge ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2121, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_SQsamebidoffer2T1LpMerge =====");
    }

    @Test
    public void testMTFAVWAPromMultiQuoteLPs_samebidoffer2T2Lps () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_samebidoffer2T2Lps =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,1.2123);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,1.2128);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,1.2120);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,1.2127);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_samebidoffer2T2Lps ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21245, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.212433, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21255, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212567, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(3, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_samebidoffer2T2Lps =====");
    }

    @Test
    public void testMTFAVWAPromMT_zeroliquidity() throws Exception {
        System.out.println("===== START : testMTFAVWAPromMT_zeroliquidity =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMT_zeroliquidity ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212033, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.2119, aggregate.getBidPrices()[3], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertNotEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212267, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.21235, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21244, aggregate.getOfferPrices()[3], 0.00001);

        //The book is sorted at the offer side as well
        assertNotEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        //LP1 gives 0 liquidity and bid quote gets dropped
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22, 0, 1000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======book1 when stream1 inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.211966, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.2119, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21184, aggregate.getBidPrices()[4], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212267, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.21235, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21244, aggregate.getOfferPrices()[3], 0.00001);

        //The book is sorted at the offer side as well
        assertNotEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMT_zeroliquidity =====");
    }

    @Test
    public void testMTFAVWAPromMultiTierLPs_merge2T () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testMTFAVWAPromMultiTierLPs_merge2T =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiTierLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2124, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.212333, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiTierLPs_merge2T =====");
    }

    @Test //PLT-3520, check after fix
    public void testMTFAVWAPromMultiTierLPs_maxliquidity () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testMTFAVWAPromMultiTierLPs_maxliquidity =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{3000,6000,7000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,1.2123);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,1.2128);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,1.2120);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,1.2127);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiTierLPs_maxliquidity ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2123, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21215, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2126, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21275, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(6000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAVWAPromMultiTierLPs_maxliquidity =====");
    }

    /**
     * Four tiers in Vwap - check scenario
     * Rates are same since rounding happening after second tier(when precision is 4), but not getting merged
     * Rates are different with precision 6
     * MergingwithroundingQT.txt. Same scenario works fine manually, mdf merges the tiers and gives the vwap agg rates
     * PLT-3586
     * @throws Exception
     */
    @Test
    public void testMTFAVWAPromMultiQuoteLPs_merge2T () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testMTFAVWAPromMultiQuoteLPs_merge2T =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000,6000,7000,8000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(4);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 6, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tier 1
        int tieridx = quote.tierOffset(QuoteC.BUY, 0);
        quote.setPrice(tieridx,1.2122);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.BUY, 1);
        quote.setPrice(tieridx, 1.2121);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.BUY, 2);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 3);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 4);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 5);
        quote.setPrice(tieridx, 1.2121);

        //tier 1
        tieridx = quote.tierOffset(QuoteC.SELL, 0);
        quote.setPrice(tieridx,1.2125);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.SELL, 1);
        quote.setPrice(tieridx, 1.2126);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.SELL, 2);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 3);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 4);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 5);
        quote.setPrice(tieridx, 1.2126);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tier 1
        tieridx = quote.tierOffset(QuoteC.BUY, 0);
        quote.setPrice(tieridx,1.2122);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.BUY, 1);
        quote.setPrice(tieridx, 1.2121);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.BUY, 2);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 3);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 4);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 5);
        quote.setPrice(tieridx, 1.2121);
        //tier 1
        tieridx = quote.tierOffset(QuoteC.SELL, 0);
        quote.setPrice(tieridx,1.2125);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.SELL, 1);
        quote.setPrice(tieridx, 1.2126);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.SELL, 2);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 3);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 4);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 5);
        quote.setPrice(tieridx, 1.2126);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2122, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        //should merge all remaining tiers since values are same after rounding up
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(8000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());


        /**  Agg quote with precision 6
         * ===== aggregate quote in testMTFAVWAPromMultiQuoteLPs_merge2T ===PriceBook [version=4, fiIndex=1001, ccyPairIndex=6029387, bookId=1,
         * bidPrices=[1.2122, 1.212166, 1.21215, 1.21214, 1.212133, 1.212128, 1.212125, 0.0, 0.0, 0.0],
         * bidQtys=[2000.0, 3000.0, 4000.0, 5000.0, 6000.0, 7000.0, 8000.0, 0.0, 0.0, 0.0],
         * bidNoLPs[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
         * offerPrices=[1.2125, 1.212534, 1.21255, 1.21256, 1.212567, 1.212572, 1.212575, 0.0, 0.0, 0.0],
         * offerQtys=[2000.0, 3000.0, 4000.0, 5000.0, 6000.0, 7000.0, 8000.0, 0.0, 0.0, 0.0],
         * offerNoLPs[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
         * timeEffective=1553063427709, numBids=7, numOffers=7, maxDepth=10, valueDate=17500, qid=0, qfime=1553063427692, flags=32]
         */

        System.out.println("===== END : testMTFAVWAPromMultiQuoteLPs_merge2T =====");
    }


    /**
     * PLT-3585 Only positive tier limits are allowed
     * 0M or negative values cannot be entered as vwap limit
     * Validate the exception raised in this case
      */
    @Test
    public void testVWAPPositiveTierLimit () throws Exception {
        System.out.println("===== START : testVWAPPositiveTierLimit =====");
        //IllegalArgumentException exception = new IllegalArgumentException();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;

        try {
            impl.setAggregationTiers(new double[]{1000,-1,0});
            System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
            impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
            RateDistributionManager manager = getNoHopManager(serverProvision);
            manager.createRateBooks(fiProvision);
        }catch (IllegalArgumentException exception) {
            System.out.println("Inside catch block 1");
            String exceptionmessage = exception.getMessage();
            assertEquals("Message is", "tier amount can't be zero or negetive. tiers=[1000.0, -1.0, 0.0]", exceptionmessage);
        }

        try {
            impl.setAggregationTiers(new double[]{0,0,0});
            System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
            impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
            RateDistributionManager manager = getNoHopManager(serverProvision);
            manager.createRateBooks(fiProvision);
        }catch (IllegalArgumentException exception) {
            System.out.println("Inside catch block 2");
            String exceptionmessage = exception.getMessage();
            assertEquals("Message is", "tier amount can't be zero or negetive. tiers=[0.0, 0.0, 0.0]", exceptionmessage);
        }
    }


    /**
     * PLT-3585 Scenario when no tiers are specified, but VWap is selected in admin
     * Change TC after fix, as of now TC passes but it should throw exception - check
     */
    @Test
    public void testVWAPNoTiers () throws Exception {
        System.out.println("===== START : testVWAPNoTiers =====");
        //IllegalArgumentException exception = new IllegalArgumentException();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;

        try {
            impl.setAggregationTiers(new double[]{});
            System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
            impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
            RateDistributionManager manager = getNoHopManager(serverProvision);
            manager.createRateBooks(fiProvision);
        }catch (IllegalArgumentException exception) {
            System.out.println("Inside catch block 1");
            String exceptionmessage = exception.getMessage();
            assertEquals("Message is", "tier amount can't be zero or negetive. tiers=[1000.0, -1.0, 0.0]", exceptionmessage);
        }
    }
}
